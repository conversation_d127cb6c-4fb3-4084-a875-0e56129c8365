"""Make payments.person NOT NULL

Revision ID: 58123809c9ef
Revises: f5bc2422cc54
Create Date: 2025-08-11 18:15:25.064613

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql as psql


# revision identifiers, used by Alembic.
revision: str = '58123809c9ef'
down_revision: Union[str, None, Sequence[str]] = 'f5bc2422cc54'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.alter_column(
        "payments",
        "person",
        existing_type=psql.UUID(as_uuid=True),
        nullable=False,
        existing_nullable=True
    )


def downgrade() -> None:
    op.alter_column(
        "payments",
        "person",
        existing_type=psql.UUID(as_uuid=True),
        nullable=True,
        existing_nullable=False
    )
