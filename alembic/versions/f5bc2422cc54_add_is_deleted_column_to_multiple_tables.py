"""Add is_deleted column to multiple tables

Revision ID: f5bc2422cc54
Revises: af0c43e85b25
Create Date: 2025-07-31 15:59:58.959013

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f5bc2422cc54'
down_revision: Union[str, None, Sequence[str]] = 'd1e2f3g4h5i6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.add_column('project_po_items', sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default=sa.false()))
    op.add_column('project_balances', sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default=sa.false()))
    op.add_column('items', sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default=sa.false()))
    op.add_column('khatabook_balance', sa.Column('is_deleted', sa.<PERSON>(), nullable=False, server_default=sa.false()))
    op.add_column('balance_details', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default=sa.false()))
    op.add_column('invoice_items', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default=sa.false()))
    op.add_column('user_item_map', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default=sa.false()))
    op.add_column('user_data', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default=sa.false()))
    op.add_column('company_info', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default=sa.false()))
    op.add_column('machinery_photos', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default=sa.false()))
    op.add_column('khatabook_payment_map', sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default=sa.false()))


def downgrade():
    op.drop_column('project_po_items', 'is_deleted')
    op.drop_column('project_balances', 'is_deleted')
    op.drop_column('items', 'is_deleted')
    op.drop_column('khatabook_balance', 'is_deleted')
    op.drop_column('balance_details', 'is_deleted')
    op.drop_column('invoice_items', 'is_deleted')
    op.drop_column('user_item_map', 'is_deleted')
    op.drop_column('user_data', 'is_deleted')
    op.drop_column('company_info', 'is_deleted')
    op.drop_column('machinery_photos', 'is_deleted')
    op.drop_column('khatabook_payment_map', 'is_deleted')