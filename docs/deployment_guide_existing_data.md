# Deployment Guide: Handling Existing Data

## Overview

When deploying the new balance recalculation logic to production, existing khatabook data may have incorrect or NULL `balance_after_entry` values. This guide explains how to handle existing data safely and efficiently.

## The Problem

1. **Existing Data**: Production has khatabook entries with potentially incorrect `balance_after_entry` values
2. **Old Logic**: Previous balance calculations may have used `created_at` instead of `expense_date` for ordering
3. **NULL Values**: Some entries may have NULL `balance_after_entry` values

## The Solution

### Automatic Fixing (Recommended)

**When any new entry is created for a user, the system will automatically detect and fix corrupted balances for that user's entire ledger.**

This happens through the `ensure_user_balances_are_correct()` method that:
- Runs automatically when creating new entries
- Only fixes balances if they are actually incorrect
- Is safe for production (non-destructive)
- Handles users with up to 1000 entries automatically
- Logs all corrections for audit purposes

### Manual Fixing (Optional)

For immediate correction of all existing data, use the provided migration script.

## Deployment Steps

### Step 1: Deploy the Code

Deploy the new balance recalculation code to production. The system will work correctly with existing data.

### Step 2: Monitor Automatic Fixes

After deployment, monitor the logs for automatic balance corrections:

```bash
# Look for these log messages
grep "Automatically fixed.*balance inconsistencies" /path/to/logs/app.log
```

### Step 3: Optional - Run Migration Script

If you want to fix all existing data immediately (not required):

```bash
# First, run a dry-run to see what would be fixed
python src/scripts/fix_existing_balance_data.py --dry-run

# If satisfied with the analysis, run the actual fix
python src/scripts/fix_existing_balance_data.py

# For specific user only
python src/scripts/fix_existing_balance_data.py --user-id "user-uuid-here"
```

## How It Works

### Automatic Detection and Fixing

When a user creates a new khatabook entry:

1. **Detection**: System checks if existing balances are correct
2. **Fixing**: If incorrect balances are found, they are automatically corrected
3. **New Entry**: The new entry is created with the correct balance
4. **Logging**: All corrections are logged for audit purposes

### Example Scenario

```
User has existing entries with incorrect balances:
Entry 1: Amount=1000, Balance=1000 ✓ (correct)
Entry 2: Amount=500,  Balance=1200 ✗ (should be 1500)
Entry 3: Amount=200,  Balance=1300 ✗ (should be 1300)

User creates new entry: Amount=300

System automatically:
1. Detects incorrect balances in entries 2 and 3
2. Fixes them: Entry 2 → 1500, Entry 3 → 1300
3. Creates new entry with correct balance: 1600
```

### Safety Features

1. **Non-Destructive**: Only updates `balance_after_entry` field
2. **Conservative**: Skips users with >1000 entries in automatic mode
3. **Transactional**: All changes are wrapped in database transactions
4. **Logged**: All corrections are logged with before/after values
5. **Backward Compatible**: Existing APIs work unchanged

## Migration Script Features

### Analysis Mode (Dry Run)
```bash
python src/scripts/fix_existing_balance_data.py --dry-run
```

**Output:**
- Number of users with corrupted balances
- Total entries that would be fixed
- Detailed statistics
- No database changes

### Batch Processing
```bash
python src/scripts/fix_existing_balance_data.py --batch-size 100
```

**Features:**
- Processes users in configurable batches
- Memory efficient for large datasets
- Progress tracking and logging
- Automatic commit points

### Specific User Fix
```bash
python src/scripts/fix_existing_balance_data.py --user-id "uuid-here"
```

**Use Cases:**
- Fix specific user reported issues
- Test the fix on a single user first
- Handle VIP users separately

## Expected Behavior After Deployment

### For Users Creating New Entries

✅ **Automatic Fix**: When any user creates a new entry, their entire ledger is automatically corrected if needed

✅ **Transparent**: Users don't notice any difference in functionality

✅ **Logged**: All corrections are logged for audit purposes

### For Users Not Creating New Entries

⏳ **Gradual Fix**: Their balances will be fixed when they next create an entry

🔧 **Manual Fix**: Can be fixed immediately using the migration script

### Performance Impact

- **Minimal**: Automatic fixing only runs for users creating new entries
- **Conservative**: Users with >1000 entries are skipped in automatic mode
- **Efficient**: Uses optimized batch processing for large ledgers

## Monitoring and Verification

### Log Messages to Monitor

```bash
# Successful automatic fixes
grep "Automatically fixed.*balance inconsistencies" app.log

# Balance recalculation operations
grep "Recalculated balances for.*entries" app.log

# Users skipped due to large ledgers
grep "entries, skipping automatic balance check" app.log
```

### Verification Queries

```sql
-- Check for NULL balance_after_entry values
SELECT COUNT(*) FROM khatabook_entries 
WHERE balance_after_entry IS NULL AND is_deleted = false;

-- Check for users with many entries (might need manual fixing)
SELECT created_by, COUNT(*) as entry_count 
FROM khatabook_entries 
WHERE is_deleted = false 
GROUP BY created_by 
HAVING COUNT(*) > 1000 
ORDER BY entry_count DESC;
```

## Rollback Plan

If issues are detected:

1. **Stop New Deployments**: Prevent further automatic fixes
2. **Database Backup**: Restore from backup if necessary
3. **Selective Rollback**: Revert specific users using backup data
4. **Manual Verification**: Check specific users reported as problematic

## FAQ

### Q: Will existing users see different balances after deployment?
**A:** Only if their balances were previously incorrect. The system will show the correct balances.

### Q: What happens to users with >1000 entries?
**A:** They are skipped in automatic mode but can be fixed using the migration script.

### Q: Is it safe to run the migration script multiple times?
**A:** Yes, it's idempotent. Running it multiple times won't cause issues.

### Q: How long does the migration script take?
**A:** Depends on data size. Approximately 1-2 seconds per user with typical entry counts.

### Q: Can I run the migration script on a live production system?
**A:** Yes, it's designed for production use with proper transaction handling and batching.

## Conclusion

The new balance recalculation system is designed to handle existing data gracefully:

1. **Automatic**: Fixes data as users interact with the system
2. **Safe**: Non-destructive and backward compatible  
3. **Efficient**: Optimized for production environments
4. **Auditable**: Comprehensive logging of all changes

Most users will have their data fixed automatically when they create new entries, making the transition seamless.
