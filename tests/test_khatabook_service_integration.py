"""
Integration tests for khatabook service with balance recalculation.

This test suite covers the integration between the service layer and repository
to ensure that the balance recalculation functionality works correctly in
real-world scenarios.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session

from src.app.services.khatabook_service import (
    create_khatabook_entry_service,
    update_khatabook_entry_service,
    soft_delete_khatabook_entry_service,
    hard_delete_khatabook_entry_service
)
from src.app.database.models import Khatabook, User, Person, Project
from src.app.schemas.constants import KHATABOOK_ENTRY_TYPE_CREDIT, KHATABOOK_ENTRY_TYPE_DEBIT


class TestKhatabookServiceIntegration:
    """Integration tests for khatabook service with balance recalculation."""

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        db = Mock(spec=Session)
        db.commit = Mock()
        db.rollback = Mock()
        db.flush = Mock()
        db.refresh = Mock()
        return db

    @pytest.fixture
    def sample_user_id(self):
        """Sample user UUID."""
        return uuid4()

    @pytest.fixture
    def sample_person_id(self):
        """Sample person UUID."""
        return uuid4()

    @pytest.fixture
    def sample_project_id(self):
        """Sample project UUID."""
        return uuid4()

    @pytest.fixture
    def sample_entry_data(self, sample_person_id, sample_project_id):
        """Sample khatabook entry data."""
        return {
            'amount': 500.0,
            'remarks': 'Test entry',
            'person_id': sample_person_id,
            'project_id': sample_project_id,
            'expense_date': datetime.now().isoformat(),
            'payment_mode': 'cash',
            'item_ids': []
        }

    @patch('src.app.services.khatabook_service.KhatabookRepository')
    def test_create_khatabook_entry_service_uses_repository(self, mock_repo_class, mock_db, sample_user_id, sample_entry_data):
        """Test that create_khatabook_entry_service uses the repository pattern."""
        # Setup mock repository
        mock_repo = Mock()
        mock_repo_class.return_value = mock_repo
        
        # Mock created entry
        mock_entry = Mock()
        mock_entry.uuid = uuid4()
        mock_entry.amount = 500.0
        mock_repo.create_entry_with_balance_calculation.return_value = mock_entry
        
        # Mock duplicate detection query
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = create_khatabook_entry_service(
            db=mock_db,
            data=sample_entry_data,
            file_paths=[],
            user_id=sample_user_id,
            current_user=sample_user_id
        )
        
        # Verify repository was used
        mock_repo_class.assert_called_once_with(mock_db)
        mock_repo.create_entry_with_balance_calculation.assert_called_once()
        
        # Verify entry data passed to repository
        call_args = mock_repo.create_entry_with_balance_calculation.call_args[1]
        assert call_args['amount'] == 500.0
        assert call_args['created_by'] == sample_user_id
        assert call_args['entry_type'] == KHATABOOK_ENTRY_TYPE_DEBIT
        
        assert result == mock_entry

    @patch('src.app.services.khatabook_service.KhatabookRepository')
    def test_update_khatabook_entry_service_uses_repository(self, mock_repo_class, mock_db, sample_entry_data):
        """Test that update_khatabook_entry_service uses the repository pattern."""
        entry_uuid = uuid4()
        
        # Setup mock repository
        mock_repo = Mock()
        mock_repo_class.return_value = mock_repo
        
        # Mock updated entry
        mock_entry = Mock()
        mock_entry.uuid = entry_uuid
        mock_repo.update_entry_with_balance_recalculation.return_value = mock_entry
        
        # Mock file operations
        mock_db.query.return_value.filter.return_value.update.return_value = 1
        
        result = update_khatabook_entry_service(
            db=mock_db,
            kb_uuid=entry_uuid,
            data=sample_entry_data,
            files=[]
        )
        
        # Verify repository was used
        mock_repo_class.assert_called_once_with(mock_db)
        mock_repo.update_entry_with_balance_recalculation.assert_called_once_with(entry_uuid, {
            'amount': 500.0,
            'remarks': 'Test entry',
            'person_id': sample_entry_data['person_id'],
            'project_id': sample_entry_data['project_id'],
            'payment_mode': 'cash'
        })
        
        assert result == mock_entry

    @patch('src.app.services.khatabook_service.KhatabookRepository')
    def test_soft_delete_khatabook_entry_service_uses_repository(self, mock_repo_class, mock_db):
        """Test that soft_delete_khatabook_entry_service uses the repository pattern."""
        entry_uuid = uuid4()
        payment_ids = [uuid4(), uuid4()]
        
        # Setup mock repository
        mock_repo = Mock()
        mock_repo_class.return_value = mock_repo
        mock_repo.soft_delete_entry_with_balance_recalculation.return_value = (True, payment_ids)
        
        success, returned_payment_ids = soft_delete_khatabook_entry_service(
            db=mock_db,
            kb_uuid=entry_uuid
        )
        
        # Verify repository was used
        mock_repo_class.assert_called_once_with(mock_db)
        mock_repo.soft_delete_entry_with_balance_recalculation.assert_called_once_with(entry_uuid)
        
        assert success is True
        assert returned_payment_ids == payment_ids
        mock_db.commit.assert_called_once()

    @patch('src.app.services.khatabook_service.KhatabookRepository')
    def test_hard_delete_khatabook_entry_service_uses_repository(self, mock_repo_class, mock_db):
        """Test that hard_delete_khatabook_entry_service uses the repository pattern."""
        entry_uuid = uuid4()
        payment_ids = [uuid4()]
        
        # Setup mock repository
        mock_repo = Mock()
        mock_repo_class.return_value = mock_repo
        mock_repo.hard_delete_entry_with_balance_recalculation.return_value = (True, payment_ids)
        
        result = hard_delete_khatabook_entry_service(
            db=mock_db,
            kb_uuid=entry_uuid
        )
        
        # Verify repository was used
        mock_repo_class.assert_called_once_with(mock_db)
        mock_repo.hard_delete_entry_with_balance_recalculation.assert_called_once_with(entry_uuid)
        
        assert result is True
        mock_db.commit.assert_called_once()

    @patch('src.app.services.khatabook_service.KhatabookRepository')
    def test_service_error_handling_with_rollback(self, mock_repo_class, mock_db):
        """Test that service methods handle errors and rollback transactions."""
        entry_uuid = uuid4()
        
        # Setup mock repository to raise exception
        mock_repo = Mock()
        mock_repo_class.return_value = mock_repo
        mock_repo.soft_delete_entry_with_balance_recalculation.side_effect = Exception("Database error")
        
        with pytest.raises(Exception, match="Database error"):
            soft_delete_khatabook_entry_service(
                db=mock_db,
                kb_uuid=entry_uuid
            )
        
        # Verify rollback was called
        mock_db.rollback.assert_called_once()

    @patch('src.app.services.khatabook_service.KhatabookRepository')
    def test_create_entry_with_items_and_files(self, mock_repo_class, mock_db, sample_user_id, sample_person_id):
        """Test creating entry with items and files integration."""
        # Setup mock repository
        mock_repo = Mock()
        mock_repo_class.return_value = mock_repo
        
        # Mock created entry
        mock_entry = Mock()
        mock_entry.uuid = uuid4()
        mock_repo.create_entry_with_balance_calculation.return_value = mock_entry
        
        # Mock item queries
        mock_item = Mock()
        mock_item.uuid = uuid4()
        mock_db.query.return_value.filter.return_value.first.side_effect = [None, mock_item]  # No duplicate, then item found
        
        entry_data = {
            'amount': 300.0,
            'remarks': 'Entry with items',
            'person_id': sample_person_id,
            'item_ids': [str(mock_item.uuid)]
        }
        
        result = create_khatabook_entry_service(
            db=mock_db,
            data=entry_data,
            file_paths=['test_file.jpg'],
            user_id=sample_user_id,
            current_user=sample_user_id
        )
        
        # Verify repository was used for entry creation
        mock_repo.create_entry_with_balance_calculation.assert_called_once()
        
        # Verify items and files were added
        assert mock_db.add.call_count >= 2  # At least entry items and files
        assert result == mock_entry

    @patch('src.app.services.khatabook_service.KhatabookRepository')
    def test_update_entry_date_triggers_recalculation(self, mock_repo_class, mock_db):
        """Test that updating entry date triggers balance recalculation."""
        entry_uuid = uuid4()
        new_date = datetime.now() + timedelta(days=1)
        
        # Setup mock repository
        mock_repo = Mock()
        mock_repo_class.return_value = mock_repo
        
        # Mock updated entry
        mock_entry = Mock()
        mock_entry.uuid = entry_uuid
        mock_repo.update_entry_with_balance_recalculation.return_value = mock_entry
        
        update_data = {
            'amount': 750.0,
            'expense_date': new_date.isoformat()
        }
        
        result = update_khatabook_entry_service(
            db=mock_db,
            kb_uuid=entry_uuid,
            data=update_data,
            files=[]
        )
        
        # Verify repository update was called with parsed date
        call_args = mock_repo.update_entry_with_balance_recalculation.call_args[0]
        update_dict = call_args[1]
        
        assert 'expense_date' in update_dict
        assert isinstance(update_dict['expense_date'], datetime)
        assert update_dict['amount'] == 750.0

    @patch('src.app.services.khatabook_service.KhatabookRepository')
    def test_backward_compatibility_maintained(self, mock_repo_class, mock_db, sample_user_id, sample_entry_data):
        """Test that existing API behavior is maintained for backward compatibility."""
        # Setup mock repository
        mock_repo = Mock()
        mock_repo_class.return_value = mock_repo
        
        # Mock created entry with expected attributes
        mock_entry = Mock()
        mock_entry.uuid = uuid4()
        mock_entry.amount = 500.0
        mock_entry.remarks = 'Test entry'
        mock_entry.created_by = sample_user_id
        mock_repo.create_entry_with_balance_calculation.return_value = mock_entry
        
        # Mock duplicate detection
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        # Call service with original API
        result = create_khatabook_entry_service(
            db=mock_db,
            data=sample_entry_data,
            file_paths=[],
            user_id=sample_user_id,
            current_user=sample_user_id
        )
        
        # Verify the result has expected structure (backward compatibility)
        assert hasattr(result, 'uuid')
        assert hasattr(result, 'amount')
        assert hasattr(result, 'remarks')
        assert result.amount == 500.0
        
        # Verify database operations still work as expected
        mock_db.commit.assert_called_once()

    def test_service_integration_with_real_data_flow(self, mock_db, sample_user_id, sample_person_id):
        """Test the complete data flow from service to repository with realistic data."""
        with patch('src.app.services.khatabook_service.KhatabookRepository') as mock_repo_class:
            # Setup realistic scenario
            mock_repo = Mock()
            mock_repo_class.return_value = mock_repo
            
            # Create a realistic entry
            mock_entry = Mock()
            mock_entry.uuid = uuid4()
            mock_entry.amount = 1250.0
            mock_entry.balance_after_entry = 3750.0  # Realistic balance after entry
            mock_entry.entry_type = KHATABOOK_ENTRY_TYPE_DEBIT
            mock_entry.expense_date = datetime.now()
            mock_repo.create_entry_with_balance_calculation.return_value = mock_entry
            
            # Mock no duplicates
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            entry_data = {
                'amount': 1250.0,
                'remarks': 'Office supplies purchase',
                'person_id': sample_person_id,
                'expense_date': datetime.now().isoformat(),
                'payment_mode': 'bank_transfer'
            }
            
            result = create_khatabook_entry_service(
                db=mock_db,
                data=entry_data,
                file_paths=[],
                user_id=sample_user_id,
                current_user=sample_user_id
            )
            
            # Verify realistic data flow
            assert result.amount == 1250.0
            assert result.balance_after_entry == 3750.0
            assert result.entry_type == KHATABOOK_ENTRY_TYPE_DEBIT
            
            # Verify repository received correct data
            repo_call_args = mock_repo.create_entry_with_balance_calculation.call_args[1]
            assert repo_call_args['amount'] == 1250.0
            assert repo_call_args['created_by'] == sample_user_id
            assert repo_call_args['entry_type'] == KHATABOOK_ENTRY_TYPE_DEBIT
