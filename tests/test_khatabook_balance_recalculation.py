"""
Comprehensive tests for khatabook balance recalculation functionality.

This test suite covers:
1. Entry creation with balance calculation
2. Entry deletion with balance recalculation
3. Entry updates with balance recalculation
4. Historical and future entry handling
5. Edge cases and error scenarios
6. Performance optimizations for large ledgers
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from src.app.database.models import K<PERSON>abook, User, Person, Project, Payment
from src.app.repositories.khatabook_repository import KhatabookRepository
from src.app.schemas.constants import KHATABOOK_ENTRY_TYPE_CREDIT, KHATABOOK_ENTRY_TYPE_DEBIT


class TestKhatabookBalanceRecalculation:
    """Test suite for khatabook balance recalculation functionality."""

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def repository(self, mock_db):
        """Khatabook repository instance."""
        return KhatabookRepository(mock_db)

    @pytest.fixture
    def sample_user_id(self):
        """Sample user UUID."""
        return uuid4()

    @pytest.fixture
    def sample_person_id(self):
        """Sample person UUID."""
        return uuid4()

    @pytest.fixture
    def sample_project_id(self):
        """Sample project UUID."""
        return uuid4()

    def test_create_entry_with_balance_calculation_first_credit(self, repository, sample_user_id, sample_person_id):
        """Test creating the first credit entry for a user."""
        # Mock no existing entries
        repository.get_user_entries_chronological = Mock(return_value=[])
        repository.get_last_entry_before_date = Mock(return_value=None)
        repository.create = Mock()
        
        # Mock the created entry
        mock_entry = Mock()
        mock_entry.uuid = uuid4()
        mock_entry.balance_after_entry = 1000.0
        repository.create.return_value = mock_entry
        
        entry_data = {
            'amount': 1000.0,
            'created_by': sample_user_id,
            'person_id': sample_person_id,
            'expense_date': datetime.now(),
            'entry_type': KHATABOOK_ENTRY_TYPE_CREDIT,
            'remarks': 'First credit entry'
        }
        
        result = repository.create_entry_with_balance_calculation(**entry_data)
        
        # Verify entry was created with correct balance
        assert result == mock_entry
        repository.create.assert_called_once()
        created_data = repository.create.call_args[1]
        assert created_data['balance_after_entry'] == 1000.0

    def test_create_entry_with_balance_calculation_first_debit(self, repository, sample_user_id, sample_person_id):
        """Test creating the first debit entry for a user."""
        # Mock no existing entries
        repository.get_user_entries_chronological = Mock(return_value=[])
        repository.get_user_available_balance = Mock(return_value=2000.0)
        repository.create = Mock()
        
        # Mock the created entry
        mock_entry = Mock()
        mock_entry.uuid = uuid4()
        mock_entry.balance_after_entry = 1500.0
        repository.create.return_value = mock_entry
        
        entry_data = {
            'amount': 500.0,
            'created_by': sample_user_id,
            'person_id': sample_person_id,
            'expense_date': datetime.now(),
            'entry_type': KHATABOOK_ENTRY_TYPE_DEBIT,
            'remarks': 'First debit entry'
        }
        
        result = repository.create_entry_with_balance_calculation(**entry_data)
        
        # Verify entry was created with correct balance
        assert result == mock_entry
        repository.create.assert_called_once()
        created_data = repository.create.call_args[1]
        assert created_data['balance_after_entry'] == 1500.0

    def test_create_historical_entry_triggers_recalculation(self, repository, sample_user_id, sample_person_id):
        """Test that creating a historical entry triggers balance recalculation."""
        now = datetime.now()
        past_date = now - timedelta(days=5)
        
        # Mock existing entries
        existing_entry = Mock()
        existing_entry.expense_date = now
        repository.get_user_entries_chronological = Mock(return_value=[existing_entry])
        repository.get_last_entry_before_date = Mock(return_value=None)
        repository.create = Mock()
        repository.recalculate_balances_from_date = Mock(return_value=1)
        repository.should_use_optimized_recalculation = Mock(return_value=False)
        
        # Mock the created entry
        mock_entry = Mock()
        mock_entry.uuid = uuid4()
        repository.create.return_value = mock_entry
        
        entry_data = {
            'amount': 500.0,
            'created_by': sample_user_id,
            'person_id': sample_person_id,
            'expense_date': past_date,
            'entry_type': KHATABOOK_ENTRY_TYPE_CREDIT,
            'remarks': 'Historical entry'
        }
        
        result = repository.create_entry_with_balance_calculation(**entry_data)
        
        # Verify recalculation was triggered
        repository.recalculate_balances_from_date.assert_called_once_with(sample_user_id, past_date)

    def test_soft_delete_entry_with_balance_recalculation(self, repository, sample_user_id):
        """Test soft deleting an entry and recalculating balances."""
        entry_uuid = uuid4()
        expense_date = datetime.now()
        
        # Mock the entry to delete
        mock_entry = Mock()
        mock_entry.created_by = sample_user_id
        mock_entry.expense_date = expense_date
        mock_entry.is_deleted = False
        
        repository.db.query.return_value.filter.return_value.first.return_value = mock_entry
        repository.db.query.return_value.filter.return_value.update.return_value = 1
        repository.db.query.return_value.filter.return_value.all.return_value = []
        repository.recalculate_balances_from_date = Mock(return_value=2)
        repository.should_use_optimized_recalculation = Mock(return_value=False)
        
        success, payment_ids = repository.soft_delete_entry_with_balance_recalculation(entry_uuid)
        
        # Verify deletion was successful
        assert success is True
        assert payment_ids == []
        assert mock_entry.is_deleted is True
        repository.recalculate_balances_from_date.assert_called_once_with(sample_user_id, expense_date)

    def test_update_entry_with_balance_recalculation(self, repository, sample_user_id):
        """Test updating an entry and recalculating balances when needed."""
        entry_uuid = uuid4()
        old_date = datetime.now() - timedelta(days=1)
        new_date = datetime.now()
        
        # Mock the entry to update
        mock_entry = Mock()
        mock_entry.created_by = sample_user_id
        mock_entry.expense_date = old_date
        mock_entry.amount = 500.0
        mock_entry.entry_type = KHATABOOK_ENTRY_TYPE_CREDIT
        
        repository.db.query.return_value.filter.return_value.first.return_value = mock_entry
        repository.get_last_entry_before_date = Mock(return_value=None)
        repository.recalculate_balances_from_date = Mock(return_value=1)
        repository.should_use_optimized_recalculation = Mock(return_value=False)
        
        update_data = {
            'expense_date': new_date,
            'amount': 750.0
        }
        
        result = repository.update_entry_with_balance_recalculation(entry_uuid, update_data)
        
        # Verify update was successful and recalculation triggered
        assert result == mock_entry
        assert mock_entry.expense_date == new_date
        assert mock_entry.amount == 750.0
        repository.recalculate_balances_from_date.assert_called_once()

    def test_calculate_running_balance(self, repository):
        """Test running balance calculation logic."""
        # Create mock entries
        credit_entry = Mock()
        credit_entry.entry_type = KHATABOOK_ENTRY_TYPE_CREDIT
        credit_entry.amount = 1000.0
        credit_entry.uuid = uuid4()
        
        debit_entry = Mock()
        debit_entry.entry_type = KHATABOOK_ENTRY_TYPE_DEBIT
        debit_entry.amount = 300.0
        debit_entry.uuid = uuid4()
        
        entries = [credit_entry, debit_entry]
        starting_balance = 0.0
        
        result = repository.calculate_running_balance(entries, starting_balance)
        
        # Verify balance calculations
        assert len(result) == 2
        assert result[0] == (credit_entry, 1000.0)  # 0 + 1000
        assert result[1] == (debit_entry, 700.0)    # 1000 - 300

    def test_validation_errors(self, repository, sample_user_id):
        """Test validation error handling."""
        # Test missing required fields
        with pytest.raises(ValueError, match="created_by.*is required"):
            repository.create_entry_with_balance_calculation(amount=100.0)
        
        # Test invalid amount
        with pytest.raises(ValueError, match="Amount must be greater than 0"):
            repository.create_entry_with_balance_calculation(
                amount=0.0,
                created_by=sample_user_id,
                expense_date=datetime.now(),
                entry_type=KHATABOOK_ENTRY_TYPE_CREDIT
            )

    def test_optimized_recalculation_threshold(self, repository, sample_user_id):
        """Test that optimized recalculation is used for large ledgers."""
        # Mock large entry count
        repository.get_user_entries_count = Mock(return_value=1000)
        
        result = repository.should_use_optimized_recalculation(sample_user_id, threshold=500)
        
        assert result is True
        
        # Mock small entry count
        repository.get_user_entries_count = Mock(return_value=100)
        
        result = repository.should_use_optimized_recalculation(sample_user_id, threshold=500)
        
        assert result is False

    def test_get_user_balance_summary(self, repository, sample_user_id):
        """Test getting comprehensive balance summary for a user."""
        # Mock latest entry
        latest_entry = Mock()
        latest_entry.balance_after_entry = 1500.0
        
        repository.db.query.return_value.filter.return_value.order_by.return_value.first.return_value = latest_entry
        repository.db.query.return_value.filter.return_value.scalar.side_effect = [2000.0, 500.0]  # credits, debits
        repository.get_user_available_balance = Mock(return_value=1800.0)
        
        result = repository.get_user_balance_summary(sample_user_id)
        
        expected = {
            'current_balance': 1500.0,
            'total_credits': 2000.0,
            'total_debits': 500.0,
            'available_balance': 1800.0,
            'net_balance': 1500.0  # 2000 - 500
        }
        
        assert result == expected

    def test_entry_not_found_scenarios(self, repository):
        """Test scenarios where entries are not found."""
        entry_uuid = uuid4()

        # Mock entry not found
        repository.db.query.return_value.filter.return_value.first.return_value = None

        # Test soft delete
        success, payment_ids = repository.soft_delete_entry_with_balance_recalculation(entry_uuid)
        assert success is False
        assert payment_ids == []

        # Test update
        result = repository.update_entry_with_balance_recalculation(entry_uuid, {'amount': 100.0})
        assert result is None

    def test_complex_chronological_scenario(self, repository, sample_user_id, sample_person_id):
        """Test complex scenario with multiple entries in different chronological order."""
        base_date = datetime.now()

        # Create entries with different dates
        entries = []
        for i, (days_offset, entry_type, amount) in enumerate([
            (-5, KHATABOOK_ENTRY_TYPE_CREDIT, 1000.0),   # Oldest
            (-3, KHATABOOK_ENTRY_TYPE_DEBIT, 200.0),     # Middle
            (-1, KHATABOOK_ENTRY_TYPE_CREDIT, 500.0),    # Recent
            (0, KHATABOOK_ENTRY_TYPE_DEBIT, 100.0),      # Latest
        ]):
            entry = Mock()
            entry.expense_date = base_date + timedelta(days=days_offset)
            entry.entry_type = entry_type
            entry.amount = amount
            entry.uuid = uuid4()
            entry.balance_after_entry = 0.0  # Will be calculated
            entries.append(entry)

        # Test running balance calculation
        result = repository.calculate_running_balance(entries, 0.0)

        # Verify balances: 0 + 1000 - 200 + 500 - 100 = 1200
        expected_balances = [1000.0, 800.0, 1300.0, 1200.0]

        for i, (entry, balance) in enumerate(result):
            assert balance == expected_balances[i]

    def test_batch_update_balances(self, repository):
        """Test batch update functionality for performance optimization."""
        # Mock database execute method
        mock_result = Mock()
        mock_result.rowcount = 3
        repository.db.execute = Mock(return_value=mock_result)
        repository.db.flush = Mock()

        balance_updates = [
            (uuid4(), 100.0),
            (uuid4(), 200.0),
            (uuid4(), 300.0),
        ]

        result = repository.batch_update_balances(balance_updates, batch_size=2)

        # Should have made 2 batch calls (2 + 1 entries)
        assert repository.db.execute.call_count == 2
        assert result == 6  # 3 + 3 (mock returns 3 for each batch)

    def test_error_handling_in_recalculation(self, repository, sample_user_id):
        """Test error handling during balance recalculation."""
        from_date = datetime.now()

        # Mock database error during flush
        repository.get_last_entry_before_date = Mock(return_value=None)
        repository.get_user_entries_chronological = Mock(return_value=[])
        repository.db.flush = Mock(side_effect=Exception("Database error"))

        with pytest.raises(Exception, match="Database error"):
            repository.recalculate_balances_from_date(sample_user_id, from_date)

    def test_validate_entry_data(self, repository):
        """Test entry data validation."""
        # Test valid data
        valid_data = {
            'amount': 100.0,
            'created_by': uuid4(),
            'expense_date': datetime.now(),
            'entry_type': KHATABOOK_ENTRY_TYPE_CREDIT
        }

        # Should not raise exception
        repository.validate_entry_data(valid_data)

        # Test invalid amount
        invalid_data = valid_data.copy()
        invalid_data['amount'] = -100.0

        with pytest.raises(ValueError, match="Amount must be a positive number"):
            repository.validate_entry_data(invalid_data)

        # Test invalid entry type
        invalid_data = valid_data.copy()
        invalid_data['entry_type'] = 'Invalid'

        with pytest.raises(ValueError, match="Invalid entry_type"):
            repository.validate_entry_data(invalid_data)

    def test_get_entry_impact_range(self, repository, sample_user_id):
        """Test calculation of entry impact range."""
        entry_uuid = uuid4()
        entry_date = datetime.now()
        last_date = datetime.now() + timedelta(days=5)

        # Mock entry
        mock_entry = Mock()
        mock_entry.expense_date = entry_date

        # Mock last entry
        mock_last_entry = Mock()
        mock_last_entry.expense_date = last_date

        repository.db.query.return_value.filter.return_value.first.side_effect = [mock_entry, mock_last_entry]

        start_date, end_date = repository.get_entry_impact_range(sample_user_id, entry_uuid)

        assert start_date == entry_date
        assert end_date == last_date

    def test_transaction_safety_wrapper(self, repository):
        """Test transaction safety wrapper functionality."""
        # Mock successful operation
        mock_operation = Mock(return_value="success")
        repository.db.in_transaction = Mock(return_value=False)
        repository.db.begin = Mock()

        result = repository.execute_with_transaction_safety(mock_operation, "arg1", kwarg1="value1")

        assert result == "success"
        mock_operation.assert_called_once_with("arg1", kwarg1="value1")

    def test_large_ledger_optimization_decision(self, repository, sample_user_id):
        """Test decision making for large ledger optimizations."""
        # Test with large ledger
        repository.get_user_entries_count = Mock(return_value=1500)

        should_optimize = repository.should_use_optimized_recalculation(sample_user_id, threshold=1000)
        assert should_optimize is True

        # Test with small ledger
        repository.get_user_entries_count = Mock(return_value=500)

        should_optimize = repository.should_use_optimized_recalculation(sample_user_id, threshold=1000)
        assert should_optimize is False

        # Test error handling
        repository.get_user_entries_count = Mock(side_effect=Exception("Database error"))

        should_optimize = repository.should_use_optimized_recalculation(sample_user_id)
        assert should_optimize is False  # Should default to False on error
