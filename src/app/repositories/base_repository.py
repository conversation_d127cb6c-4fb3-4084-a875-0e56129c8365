"""
Base repository class providing common database operations.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from src.app.utils.logging_config import get_database_logger

# Type variable for model classes
ModelType = TypeVar("ModelType")

# Initialize logger
db_logger = get_database_logger()


class BaseRepository(Generic[ModelType], ABC):
    """
    Base repository class providing common CRUD operations.
    
    This class follows the Repository pattern to encapsulate database access logic
    and provide a consistent interface for data operations.
    """
    
    def __init__(self, model: Type[ModelType], db: Session):
        """
        Initialize the repository with a model class and database session.
        
        Args:
            model: The SQLAlchemy model class
            db: Database session
        """
        self.model = model
        self.db = db
    
    def get_by_id(self, id: int) -> Optional[ModelType]:
        """Get a record by its primary key ID."""
        try:
            return self.db.query(self.model).filter(self.model.id == id).first()
        except SQLAlchemyError as e:
            db_logger.error(f"Error getting {self.model.__name__} by id {id}: {str(e)}")
            raise
    
    def get_by_uuid(self, uuid: UUID) -> Optional[ModelType]:
        """Get a record by its UUID."""
        try:
            return self.db.query(self.model).filter(self.model.uuid == uuid).first()
        except SQLAlchemyError as e:
            db_logger.error(f"Error getting {self.model.__name__} by uuid {uuid}: {str(e)}")
            raise
    
    def get_all(self, include_deleted: bool = False) -> List[ModelType]:
        """Get all records, optionally including soft-deleted ones."""
        try:
            query = self.db.query(self.model)
            if hasattr(self.model, 'is_deleted') and not include_deleted:
                query = query.filter(self.model.is_deleted.is_(False))
            return query.all()
        except SQLAlchemyError as e:
            db_logger.error(f"Error getting all {self.model.__name__} records: {str(e)}")
            raise
    
    def create(self, **kwargs) -> ModelType:
        """Create a new record."""
        try:
            instance = self.model(**kwargs)
            self.db.add(instance)
            self.db.flush()  # Flush to get the ID without committing
            return instance
        except SQLAlchemyError as e:
            db_logger.error(f"Error creating {self.model.__name__}: {str(e)}")
            raise
    
    def update(self, instance: ModelType, **kwargs) -> ModelType:
        """Update an existing record."""
        try:
            for key, value in kwargs.items():
                if hasattr(instance, key):
                    setattr(instance, key, value)
            self.db.flush()
            return instance
        except SQLAlchemyError as e:
            db_logger.error(f"Error updating {self.model.__name__}: {str(e)}")
            raise
    
    def soft_delete(self, instance: ModelType) -> bool:
        """Soft delete a record by setting is_deleted=True."""
        try:
            if hasattr(instance, 'is_deleted'):
                instance.is_deleted = True
                self.db.flush()
                return True
            else:
                db_logger.warning(f"{self.model.__name__} does not support soft delete")
                return False
        except SQLAlchemyError as e:
            db_logger.error(f"Error soft deleting {self.model.__name__}: {str(e)}")
            raise
    
    def hard_delete(self, instance: ModelType) -> bool:
        """Hard delete a record from the database."""
        try:
            self.db.delete(instance)
            self.db.flush()
            return True
        except SQLAlchemyError as e:
            db_logger.error(f"Error hard deleting {self.model.__name__}: {str(e)}")
            raise
    
    def count(self, include_deleted: bool = False) -> int:
        """Count total records."""
        try:
            query = self.db.query(self.model)
            if hasattr(self.model, 'is_deleted') and not include_deleted:
                query = query.filter(self.model.is_deleted.is_(False))
            return query.count()
        except SQLAlchemyError as e:
            db_logger.error(f"Error counting {self.model.__name__} records: {str(e)}")
            raise
    
    def exists(self, **filters) -> bool:
        """Check if a record exists with the given filters."""
        try:
            query = self.db.query(self.model)
            for key, value in filters.items():
                if hasattr(self.model, key):
                    query = query.filter(getattr(self.model, key) == value)
            return query.first() is not None
        except SQLAlchemyError as e:
            db_logger.error(f"Error checking existence in {self.model.__name__}: {str(e)}")
            raise
    
    def commit(self):
        """Commit the current transaction."""
        try:
            self.db.commit()
        except SQLAlchemyError as e:
            db_logger.error(f"Error committing transaction: {str(e)}")
            self.db.rollback()
            raise
    
    def rollback(self):
        """Rollback the current transaction."""
        try:
            self.db.rollback()
        except SQLAlchemyError as e:
            db_logger.error(f"Error rolling back transaction: {str(e)}")
            raise
    
    def flush(self):
        """Flush pending changes to the database."""
        try:
            self.db.flush()
        except SQLAlchemyError as e:
            db_logger.error(f"Error flushing changes: {str(e)}")
            raise
