"""
Khatabook repository with comprehensive balance recalculation logic.
"""

from datetime import datetime
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, asc, desc, func
from sqlalchemy.orm import Session, joinedload

from src.app.database.models import (
    Khatabook,
    KhatabookBalance,
    KhatabookFile,
    KhatabookItem,
    Payment,
)
from src.app.repositories.base_repository import BaseRepository
from src.app.schemas.constants import K<PERSON><PERSON>BOOK_ENTRY_TYPE_CREDIT, KHATABOOK_ENTRY_TYPE_DEBIT
from src.app.utils.logging_config import get_database_logger

# Initialize logger
db_logger = get_database_logger()


class KhatabookRepository(BaseRepository[Khatabook]):
    """
    Repository for Khatabook operations with comprehensive balance recalculation logic.
    
    This repository handles:
    1. Creating new credit/debit khatabook entries with correct balance_after_entry
    2. Recalculating balances when entries are deleted from any position
    3. Handling historical and future entry insertions
    4. Maintaining chronological ordering based on expense_date
    """
    
    def __init__(self, db: Session):
        super().__init__(K<PERSON>ab<PERSON>, db)
    
    def get_user_entries_chronological(
        self, 
        user_id: UUID, 
        include_deleted: bool = False,
        from_date: Optional[datetime] = None
    ) -> List[Khatabook]:
        """
        Get user's khatabook entries in chronological order by expense_date.
        
        Args:
            user_id: User UUID
            include_deleted: Whether to include soft-deleted entries
            from_date: Optional date to filter entries from (inclusive)
            
        Returns:
            List of khatabook entries ordered by expense_date, then by created_at
        """
        try:
            query = (
                self.db.query(Khatabook)
                .filter(Khatabook.created_by == user_id)
            )
            
            if not include_deleted:
                query = query.filter(Khatabook.is_deleted.is_(False))
            
            if from_date:
                query = query.filter(Khatabook.expense_date >= from_date)
            
            # Order by expense_date first, then by created_at for entries on same date
            # Use id as final tiebreaker for consistent ordering
            entries = query.order_by(
                asc(Khatabook.expense_date),
                asc(Khatabook.created_at),
                asc(Khatabook.id)
            ).all()
            
            db_logger.debug(f"Retrieved {len(entries)} chronological entries for user {user_id}")
            return entries
            
        except Exception as e:
            db_logger.error(f"Error getting chronological entries for user {user_id}: {str(e)}")
            raise
    
    def get_last_entry_before_date(self, user_id: UUID, before_date: datetime) -> Optional[Khatabook]:
        """
        Get the last khatabook entry before a specific date for balance calculation.
        
        Args:
            user_id: User UUID
            before_date: Date to search before
            
        Returns:
            Last entry before the given date, or None if no entries exist
        """
        try:
            entry = (
                self.db.query(Khatabook)
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.is_deleted.is_(False),
                    Khatabook.expense_date < before_date
                )
                .order_by(
                    desc(Khatabook.expense_date),
                    desc(Khatabook.created_at),
                    desc(Khatabook.id)
                )
                .first()
            )
            
            db_logger.debug(f"Found last entry before {before_date} for user {user_id}: {entry.uuid if entry else None}")
            return entry
            
        except Exception as e:
            db_logger.error(f"Error getting last entry before date for user {user_id}: {str(e)}")
            raise
    
    def calculate_running_balance(self, entries: List[Khatabook], starting_balance: float = 0.0) -> List[Tuple[Khatabook, float]]:
        """
        Calculate running balance for a list of chronologically ordered entries.
        
        Args:
            entries: List of khatabook entries in chronological order
            starting_balance: Starting balance before the first entry
            
        Returns:
            List of tuples (entry, balance_after_entry)
        """
        try:
            result = []
            current_balance = starting_balance
            
            for entry in entries:
                if entry.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT:
                    current_balance += entry.amount
                elif entry.entry_type == KHATABOOK_ENTRY_TYPE_DEBIT:
                    current_balance -= entry.amount
                else:
                    db_logger.warning(f"Unknown entry type '{entry.entry_type}' for entry {entry.uuid}")
                    continue
                
                result.append((entry, current_balance))
                db_logger.debug(f"Entry {entry.uuid}: {entry.entry_type} {entry.amount} -> Balance: {current_balance}")
            
            return result
            
        except Exception as e:
            db_logger.error(f"Error calculating running balance: {str(e)}")
            raise
    
    def get_user_available_balance(self, user_id: UUID) -> float:
        """
        Calculate user's available balance from transferred self-payments minus total spent.
        This is used for debit entry balance calculations.
        
        Args:
            user_id: User UUID
            
        Returns:
            Available balance amount
        """
        try:
            # Get total from transferred self-payments
            transferred_self_payments = (
                self.db.query(Payment)
                .filter(
                    Payment.created_by == user_id,
                    Payment.self_payment == True,
                    Payment.status == "transferred",
                    Payment.is_deleted == False,
                )
                .all()
            )
            
            user_available_balance = sum(payment.amount for payment in transferred_self_payments)
            
            # Get total spent (debit entries)
            total_spent = (
                self.db.query(func.sum(Khatabook.amount))
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_DEBIT,
                    Khatabook.is_deleted == False
                )
                .scalar() or 0.0
            )
            
            available_balance = user_available_balance - total_spent
            
            db_logger.debug(f"User {user_id} available balance: {user_available_balance} - {total_spent} = {available_balance}")
            return available_balance
            
        except Exception as e:
            db_logger.error(f"Error calculating available balance for user {user_id}: {str(e)}")
            raise

    def recalculate_balances_from_date(self, user_id: UUID, from_date: datetime) -> int:
        """
        Recalculate balance_after_entry for all entries from a specific date onwards.

        This method is called when:
        1. A new entry is inserted with a past expense_date
        2. An entry is deleted from the middle of the chronological sequence
        3. An entry's expense_date is modified

        Args:
            user_id: User UUID
            from_date: Date from which to recalculate balances (inclusive)

        Returns:
            Number of entries updated

        Raises:
            ValueError: If user_id is invalid or from_date is None
            SQLAlchemyError: If database operations fail
        """
        if not user_id:
            raise ValueError("user_id cannot be None or empty")
        if not from_date:
            raise ValueError("from_date cannot be None")

        try:
            db_logger.info(f"Starting balance recalculation for user {user_id} from date {from_date}")

            # Get the last entry before the from_date to establish starting balance
            last_entry_before = self.get_last_entry_before_date(user_id, from_date)
            starting_balance = last_entry_before.balance_after_entry if last_entry_before else 0.0

            # Get all entries from the specified date onwards
            entries_to_update = self.get_user_entries_chronological(user_id, from_date=from_date)

            if not entries_to_update:
                db_logger.info(f"No entries found to recalculate for user {user_id} from {from_date}")
                return 0

            # Validate entries before processing
            for entry in entries_to_update:
                if not entry.amount or entry.amount < 0:
                    raise ValueError(f"Invalid amount {entry.amount} for entry {entry.uuid}")
                if not entry.entry_type or entry.entry_type not in [KHATABOOK_ENTRY_TYPE_CREDIT, KHATABOOK_ENTRY_TYPE_DEBIT]:
                    raise ValueError(f"Invalid entry_type {entry.entry_type} for entry {entry.uuid}")

            # Calculate new balances
            balance_calculations = self.calculate_running_balance(entries_to_update, starting_balance)

            # Update entries with new balance_after_entry values
            updated_count = 0
            for entry, new_balance in balance_calculations:
                if entry.balance_after_entry != new_balance:
                    old_balance = entry.balance_after_entry
                    entry.balance_after_entry = new_balance
                    updated_count += 1
                    db_logger.debug(f"Updated entry {entry.uuid} balance from {old_balance} to {new_balance}")

            # Flush changes to database with error handling
            try:
                self.db.flush()
            except Exception as flush_error:
                db_logger.error(f"Failed to flush balance updates: {str(flush_error)}")
                raise

            db_logger.info(f"Successfully recalculated balances for {updated_count} entries for user {user_id}")
            return updated_count

        except ValueError as ve:
            db_logger.error(f"Validation error in balance recalculation for user {user_id}: {str(ve)}")
            raise
        except Exception as e:
            db_logger.error(f"Unexpected error recalculating balances from date for user {user_id}: {str(e)}")
            # Don't rollback here as this method is called within larger transactions
            raise

    def create_entry_with_balance_calculation(self, **entry_data) -> Khatabook:
        """
        Create a new khatabook entry with proper balance_after_entry calculation.

        This method handles both credit and debit entries and determines whether
        balance recalculation is needed for existing entries. It properly handles:
        1. Historical entries (past dates)
        2. Future entries (future dates)
        3. Entries on the same date as existing entries
        4. First entry for a user

        Args:
            **entry_data: Khatabook entry data

        Returns:
            Created khatabook entry with correct balance_after_entry
        """
        try:
            user_id = entry_data.get('created_by')
            expense_date = entry_data.get('expense_date', datetime.now())
            entry_type = entry_data.get('entry_type', KHATABOOK_ENTRY_TYPE_DEBIT)
            amount = entry_data.get('amount', 0.0)

            if not user_id:
                raise ValueError("created_by (user_id) is required")

            if amount <= 0:
                raise ValueError("Amount must be greater than 0")

            db_logger.info(f"Creating {entry_type} entry for user {user_id} with amount {amount} on {expense_date}")

            # Ensure existing balances are correct before creating new entry
            # This automatically fixes any corrupted data from previous versions
            self.ensure_user_balances_are_correct(user_id)

            # Get all existing entries for chronological analysis
            existing_entries = self.get_user_entries_chronological(user_id)

            # Determine entry position in chronological sequence
            is_historical_entry = False
            is_future_entry = False

            if existing_entries:
                earliest_entry = existing_entries[0]
                latest_entry = existing_entries[-1]

                is_historical_entry = expense_date < earliest_entry.expense_date
                is_future_entry = expense_date > latest_entry.expense_date

                db_logger.debug(f"Entry analysis: historical={is_historical_entry}, future={is_future_entry}")

            # Calculate balance_after_entry for the new entry
            if entry_type == KHATABOOK_ENTRY_TYPE_DEBIT:
                # For debit entries, calculate based on available balance
                # This represents the remaining balance after this expense
                current_available_balance = self.get_user_available_balance(user_id)
                balance_after_entry = current_available_balance - amount

                db_logger.debug(f"Debit entry: available_balance={current_available_balance}, after_expense={balance_after_entry}")

            elif entry_type == KHATABOOK_ENTRY_TYPE_CREDIT:
                # For credit entries, use running balance calculation
                # Find the balance just before this entry's date
                last_entry_before = self.get_last_entry_before_date(user_id, expense_date)
                starting_balance = last_entry_before.balance_after_entry if last_entry_before else 0.0
                balance_after_entry = starting_balance + amount

                db_logger.debug(f"Credit entry: starting_balance={starting_balance}, after_credit={balance_after_entry}")

            else:
                raise ValueError(f"Invalid entry_type: {entry_type}")

            # Create the entry with calculated balance
            entry_data['balance_after_entry'] = balance_after_entry
            new_entry = self.create(**entry_data)

            db_logger.info(f"Created entry {new_entry.uuid} with balance_after_entry: {balance_after_entry}")

            # Determine if recalculation is needed for existing entries
            needs_recalculation = is_historical_entry or (existing_entries and not is_future_entry)

            if needs_recalculation:
                db_logger.info(f"Recalculation needed - recalculating balances from {expense_date}")
                # Flush the new entry before recalculating others
                self.db.flush()
                # Use optimized recalculation for users with many entries
                if self.should_use_optimized_recalculation(user_id):
                    updated_count = self.recalculate_balances_optimized(user_id, expense_date)
                else:
                    updated_count = self.recalculate_balances_from_date(user_id, expense_date)
                db_logger.info(f"Recalculated balances for {updated_count} entries")
            else:
                db_logger.info(f"No recalculation needed - entry is chronologically last")

            return new_entry

        except Exception as e:
            db_logger.error(f"Error creating entry with balance calculation: {str(e)}")
            raise

    def soft_delete_entry_with_balance_recalculation(self, entry_uuid: UUID) -> Tuple[bool, List[UUID]]:
        """
        Soft delete a khatabook entry and recalculate balances for subsequent entries.

        This method:
        1. Soft deletes the khatabook entry and related files/items
        2. Soft deletes linked payment entries
        3. Recalculates balance_after_entry for all subsequent entries
        4. Maintains transaction integrity

        Args:
            entry_uuid: UUID of the khatabook entry to delete

        Returns:
            Tuple of (success: bool, linked_payment_ids: List[UUID])

        Raises:
            ValueError: If entry_uuid is invalid
            SQLAlchemyError: If database operations fail
        """
        if not entry_uuid:
            raise ValueError("entry_uuid cannot be None or empty")

        try:
            # Fetch the khatabook entry
            entry = self.db.query(Khatabook).filter(
                Khatabook.uuid == entry_uuid,
                Khatabook.is_deleted.is_(False)
            ).first()

            if not entry:
                db_logger.warning(f"Khatabook entry {entry_uuid} not found or already deleted")
                return False, []

            user_id = entry.created_by
            expense_date = entry.expense_date

            if not user_id or not expense_date:
                raise ValueError(f"Entry {entry_uuid} has invalid user_id or expense_date")

            db_logger.info(f"Soft deleting khatabook entry {entry_uuid} for user {user_id}")

            # Soft delete related files with error handling
            try:
                files_updated = (
                    self.db.query(KhatabookFile)
                    .filter(
                        KhatabookFile.khatabook_id == entry_uuid,
                        KhatabookFile.is_deleted.is_(False)
                    )
                    .update({KhatabookFile.is_deleted: True})
                )
                db_logger.info(f"Marked {files_updated} files as deleted for khatabook {entry_uuid}")
            except Exception as file_error:
                db_logger.error(f"Error deleting files for entry {entry_uuid}: {str(file_error)}")
                raise

            # Soft delete related items with error handling
            try:
                items_updated = (
                    self.db.query(KhatabookItem)
                    .filter(
                        KhatabookItem.khatabook_id == entry_uuid,
                        KhatabookItem.is_deleted.is_(False)
                    )
                    .update({KhatabookItem.is_deleted: True})
                )
                db_logger.info(f"Marked {items_updated} items as deleted for khatabook {entry_uuid}")
            except Exception as item_error:
                db_logger.error(f"Error deleting items for entry {entry_uuid}: {str(item_error)}")
                raise

            # Find and soft delete linked payments with error handling
            try:
                from src.app.database.models import KhatabookPaymentMap
                payment_links = self.db.query(KhatabookPaymentMap).filter(
                    KhatabookPaymentMap.khatabook_id == entry_uuid
                ).all()

                payment_ids = [link.payment_id for link in payment_links]

                if payment_ids:
                    payments_updated = (
                        self.db.query(Payment)
                        .filter(
                            Payment.uuid.in_(payment_ids),
                            Payment.is_deleted.is_(False)
                        )
                        .update({Payment.is_deleted: True}, synchronize_session=False)
                    )
                    db_logger.info(f"Soft deleted {payments_updated} payment(s) linked to khatabook {entry_uuid}")
                else:
                    db_logger.info(f"No payments linked to khatabook {entry_uuid}")
            except Exception as payment_error:
                db_logger.error(f"Error deleting linked payments for entry {entry_uuid}: {str(payment_error)}")
                raise

            # Soft delete the khatabook entry itself
            entry.is_deleted = True
            db_logger.info(f"Marked khatabook entry {entry_uuid} as deleted")

            # Flush changes before recalculating balances
            try:
                self.db.flush()
            except Exception as flush_error:
                db_logger.error(f"Failed to flush deletion changes for entry {entry_uuid}: {str(flush_error)}")
                raise

            # Recalculate balances for all entries from this date onwards
            # This ensures that the deletion impact is properly reflected
            try:
                # Use optimized recalculation for users with many entries
                if self.should_use_optimized_recalculation(user_id):
                    updated_count = self.recalculate_balances_optimized(user_id, expense_date)
                else:
                    updated_count = self.recalculate_balances_from_date(user_id, expense_date)
                db_logger.info(f"Recalculated balances for {updated_count} entries after deletion")
            except Exception as recalc_error:
                db_logger.error(f"Failed to recalculate balances after deleting entry {entry_uuid}: {str(recalc_error)}")
                raise

            return True, payment_ids

        except ValueError as ve:
            db_logger.error(f"Validation error in soft delete for entry {entry_uuid}: {str(ve)}")
            raise
        except Exception as e:
            db_logger.error(f"Unexpected error soft deleting entry {entry_uuid}: {str(e)}")
            raise

    def hard_delete_entry_with_balance_recalculation(self, entry_uuid: UUID) -> Tuple[bool, List[UUID]]:
        """
        Hard delete a khatabook entry and recalculate balances for subsequent entries.

        This method:
        1. Hard deletes the khatabook entry and related files/items
        2. Hard deletes linked payment entries
        3. Recalculates balance_after_entry for all subsequent entries
        4. Maintains transaction integrity

        Args:
            entry_uuid: UUID of the khatabook entry to delete

        Returns:
            Tuple of (success: bool, linked_payment_ids: List[UUID])
        """
        try:
            # Fetch the khatabook entry before deletion to get user_id and expense_date
            entry = self.db.query(Khatabook).filter(Khatabook.uuid == entry_uuid).first()

            if not entry:
                db_logger.warning(f"Khatabook entry {entry_uuid} not found")
                return False, []

            user_id = entry.created_by
            expense_date = entry.expense_date

            db_logger.info(f"Hard deleting khatabook entry {entry_uuid} for user {user_id}")

            # Find linked payments before deletion
            from src.app.database.models import KhatabookPaymentMap
            payment_links = self.db.query(KhatabookPaymentMap).filter(
                KhatabookPaymentMap.khatabook_id == entry_uuid
            ).all()

            payment_ids = [link.payment_id for link in payment_links]

            # Hard delete related files
            files_deleted = self.db.query(KhatabookFile).filter(
                KhatabookFile.khatabook_id == entry_uuid
            ).delete()
            db_logger.info(f"Hard deleted {files_deleted} files for khatabook {entry_uuid}")

            # Hard delete related items
            items_deleted = self.db.query(KhatabookItem).filter(
                KhatabookItem.khatabook_id == entry_uuid
            ).delete()
            db_logger.info(f"Hard deleted {items_deleted} items for khatabook {entry_uuid}")

            # Hard delete payment mappings
            mappings_deleted = self.db.query(KhatabookPaymentMap).filter(
                KhatabookPaymentMap.khatabook_id == entry_uuid
            ).delete()
            db_logger.info(f"Hard deleted {mappings_deleted} payment mappings for khatabook {entry_uuid}")

            # Hard delete linked payments
            if payment_ids:
                payments_deleted = self.db.query(Payment).filter(
                    Payment.uuid.in_(payment_ids)
                ).delete(synchronize_session=False)
                db_logger.info(f"Hard deleted {payments_deleted} payment(s) linked to khatabook {entry_uuid}")

            # Hard delete the khatabook entry itself
            entry_deleted = self.db.query(Khatabook).filter(Khatabook.uuid == entry_uuid).delete()
            db_logger.info(f"Hard deleted khatabook entry {entry_uuid}")

            # Flush changes before recalculating balances
            self.db.flush()

            # Recalculate balances for all entries from this date onwards
            # This ensures that the deletion impact is properly reflected
            updated_count = self.recalculate_balances_from_date(user_id, expense_date)
            db_logger.info(f"Recalculated balances for {updated_count} entries after hard deletion")

            return entry_deleted > 0, payment_ids

        except Exception as e:
            db_logger.error(f"Error hard deleting entry {entry_uuid}: {str(e)}")
            raise

    def update_entry_with_balance_recalculation(
        self,
        entry_uuid: UUID,
        update_data: Dict
    ) -> Optional[Khatabook]:
        """
        Update a khatabook entry and recalculate balances if necessary.

        This method handles updates to entry amounts, dates, or types and triggers
        balance recalculation when needed.

        Args:
            entry_uuid: UUID of the khatabook entry to update
            update_data: Dictionary containing fields to update

        Returns:
            Updated khatabook entry or None if not found
        """
        try:
            # Fetch the existing entry
            entry = self.db.query(Khatabook).filter(
                Khatabook.uuid == entry_uuid,
                Khatabook.is_deleted.is_(False)
            ).first()

            if not entry:
                db_logger.warning(f"Khatabook entry {entry_uuid} not found or already deleted")
                return None

            user_id = entry.created_by
            old_expense_date = entry.expense_date
            old_amount = entry.amount
            old_entry_type = entry.entry_type

            # Extract new values from update_data
            new_expense_date = update_data.get('expense_date', old_expense_date)
            new_amount = update_data.get('amount', old_amount)
            new_entry_type = update_data.get('entry_type', old_entry_type)

            db_logger.info(f"Updating khatabook entry {entry_uuid} for user {user_id}")

            # Determine if balance recalculation is needed
            needs_recalculation = (
                new_expense_date != old_expense_date or
                new_amount != old_amount or
                new_entry_type != old_entry_type
            )

            # Update the entry fields
            for field, value in update_data.items():
                if hasattr(entry, field):
                    setattr(entry, field, value)

            if needs_recalculation:
                db_logger.info(f"Balance recalculation needed for entry {entry_uuid}")

                # Determine the earliest date that needs recalculation
                earliest_date = min(old_expense_date, new_expense_date)

                # If the expense_date changed, we need to recalculate the entry's balance
                if new_expense_date != old_expense_date:
                    # Calculate new balance for the updated entry
                    if new_entry_type == KHATABOOK_ENTRY_TYPE_DEBIT:
                        # For debit entries, use available balance calculation
                        current_available_balance = self.get_user_available_balance(user_id)
                        # Adjust for the old entry being removed and new entry being added
                        if old_entry_type == KHATABOOK_ENTRY_TYPE_DEBIT:
                            current_available_balance += old_amount  # Remove old debit
                        else:
                            current_available_balance -= old_amount  # Remove old credit
                        entry.balance_after_entry = current_available_balance - new_amount
                    else:
                        # For credit entries, use running balance calculation
                        last_entry_before = self.get_last_entry_before_date(user_id, new_expense_date)
                        starting_balance = last_entry_before.balance_after_entry if last_entry_before else 0.0
                        entry.balance_after_entry = starting_balance + new_amount

                # Flush the entry update before recalculating other entries
                self.db.flush()

                # Recalculate balances for all entries from the earliest affected date
                updated_count = self.recalculate_balances_from_date(user_id, earliest_date)
                db_logger.info(f"Recalculated balances for {updated_count} entries after update")

            else:
                db_logger.info(f"No balance recalculation needed for entry {entry_uuid}")

            return entry

        except Exception as e:
            db_logger.error(f"Error updating entry {entry_uuid}: {str(e)}")
            raise

    def get_user_balance_summary(self, user_id: UUID) -> Dict[str, float]:
        """
        Get a comprehensive balance summary for a user.

        Returns:
            Dictionary containing balance information
        """
        try:
            # Get latest entry to get current balance
            latest_entry = (
                self.db.query(Khatabook)
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.is_deleted.is_(False)
                )
                .order_by(
                    desc(Khatabook.expense_date),
                    desc(Khatabook.created_at),
                    desc(Khatabook.id)
                )
                .first()
            )

            current_balance = latest_entry.balance_after_entry if latest_entry else 0.0

            # Get total credits and debits
            credit_total = (
                self.db.query(func.sum(Khatabook.amount))
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT,
                    Khatabook.is_deleted.is_(False)
                )
                .scalar() or 0.0
            )

            debit_total = (
                self.db.query(func.sum(Khatabook.amount))
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_DEBIT,
                    Khatabook.is_deleted.is_(False)
                )
                .scalar() or 0.0
            )

            # Get available balance (for debit calculations)
            available_balance = self.get_user_available_balance(user_id)

            return {
                'current_balance': current_balance,
                'total_credits': credit_total,
                'total_debits': debit_total,
                'available_balance': available_balance,
                'net_balance': credit_total - debit_total
            }

        except Exception as e:
            db_logger.error(f"Error getting balance summary for user {user_id}: {str(e)}")
            raise

    def execute_with_transaction_safety(self, operation_func, *args, **kwargs):
        """
        Execute a repository operation with proper transaction safety.

        This method ensures that operations are executed within a transaction
        and provides proper rollback on errors.

        Args:
            operation_func: The repository method to execute
            *args: Arguments to pass to the operation
            **kwargs: Keyword arguments to pass to the operation

        Returns:
            Result of the operation

        Raises:
            Exception: Re-raises any exception from the operation after logging
        """
        try:
            # Check if we're already in a transaction
            if self.db.in_transaction():
                # Already in transaction, just execute the operation
                return operation_func(*args, **kwargs)
            else:
                # Start a new transaction
                with self.db.begin():
                    return operation_func(*args, **kwargs)

        except Exception as e:
            db_logger.error(f"Transaction failed for operation {operation_func.__name__}: {str(e)}")
            # The transaction will be automatically rolled back by the context manager
            raise

    def validate_entry_data(self, entry_data: Dict) -> None:
        """
        Validate khatabook entry data before processing.

        Args:
            entry_data: Dictionary containing entry data

        Raises:
            ValueError: If validation fails
        """
        required_fields = ['amount', 'created_by', 'expense_date', 'entry_type']

        for field in required_fields:
            if field not in entry_data or entry_data[field] is None:
                raise ValueError(f"Required field '{field}' is missing or None")

        # Validate amount
        amount = entry_data.get('amount')
        if not isinstance(amount, (int, float)) or amount <= 0:
            raise ValueError(f"Amount must be a positive number, got: {amount}")

        # Validate entry_type
        entry_type = entry_data.get('entry_type')
        if entry_type not in [KHATABOOK_ENTRY_TYPE_CREDIT, KHATABOOK_ENTRY_TYPE_DEBIT]:
            raise ValueError(f"Invalid entry_type: {entry_type}")

        # Validate expense_date
        expense_date = entry_data.get('expense_date')
        if not isinstance(expense_date, datetime):
            raise ValueError(f"expense_date must be a datetime object, got: {type(expense_date)}")

        db_logger.debug(f"Entry data validation passed for amount: {amount}, type: {entry_type}")

    def get_entry_impact_range(self, user_id: UUID, entry_uuid: UUID) -> Tuple[datetime, datetime]:
        """
        Get the date range that would be impacted by changes to a specific entry.

        This helps optimize balance recalculation by determining the minimal
        range of entries that need to be updated.

        Args:
            user_id: User UUID
            entry_uuid: Entry UUID to analyze

        Returns:
            Tuple of (start_date, end_date) for the impact range
        """
        try:
            entry = self.db.query(Khatabook).filter(
                Khatabook.uuid == entry_uuid,
                Khatabook.created_by == user_id
            ).first()

            if not entry:
                raise ValueError(f"Entry {entry_uuid} not found for user {user_id}")

            # Impact starts from this entry's date
            start_date = entry.expense_date

            # Impact goes to the last entry for this user
            last_entry = (
                self.db.query(Khatabook)
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.is_deleted.is_(False)
                )
                .order_by(desc(Khatabook.expense_date), desc(Khatabook.created_at))
                .first()
            )

            end_date = last_entry.expense_date if last_entry else start_date

            db_logger.debug(f"Impact range for entry {entry_uuid}: {start_date} to {end_date}")
            return start_date, end_date

        except Exception as e:
            db_logger.error(f"Error calculating impact range for entry {entry_uuid}: {str(e)}")
            raise

    def batch_update_balances(self, balance_updates: List[Tuple[UUID, float]], batch_size: int = 100) -> int:
        """
        Perform batch updates of balance_after_entry values for improved performance.

        This method is optimized for large numbers of balance updates by using
        batch operations instead of individual updates.

        Args:
            balance_updates: List of tuples (entry_uuid, new_balance)
            batch_size: Number of updates to process in each batch

        Returns:
            Number of entries updated
        """
        try:
            total_updated = 0

            # Process updates in batches
            for i in range(0, len(balance_updates), batch_size):
                batch = balance_updates[i:i + batch_size]

                # Build batch update query
                update_cases = []
                entry_uuids = []

                for entry_uuid, new_balance in batch:
                    update_cases.append(f"WHEN uuid = '{entry_uuid}' THEN {new_balance}")
                    entry_uuids.append(entry_uuid)

                if update_cases:
                    # Use raw SQL for efficient batch update
                    case_statement = " ".join(update_cases)
                    update_query = f"""
                        UPDATE khatabook_entries
                        SET balance_after_entry = CASE
                            {case_statement}
                            ELSE balance_after_entry
                        END
                        WHERE uuid IN ({','.join([f"'{uuid}'" for uuid in entry_uuids])})
                    """

                    result = self.db.execute(update_query)
                    batch_updated = result.rowcount
                    total_updated += batch_updated

                    db_logger.debug(f"Batch updated {batch_updated} entries (batch {i//batch_size + 1})")

            # Flush all changes
            self.db.flush()

            db_logger.info(f"Batch updated {total_updated} entries total")
            return total_updated

        except Exception as e:
            db_logger.error(f"Error in batch update balances: {str(e)}")
            raise

    def recalculate_balances_optimized(self, user_id: UUID, from_date: datetime, batch_size: int = 100) -> int:
        """
        Optimized version of balance recalculation for large ledgers.

        This method uses batch processing and efficient queries to handle
        users with thousands of khatabook entries.

        Args:
            user_id: User UUID
            from_date: Date from which to recalculate balances (inclusive)
            batch_size: Number of entries to process in each batch

        Returns:
            Number of entries updated
        """
        try:
            db_logger.info(f"Starting optimized balance recalculation for user {user_id} from {from_date}")

            # Get starting balance efficiently
            last_entry_before = self.get_last_entry_before_date(user_id, from_date)
            starting_balance = last_entry_before.balance_after_entry if last_entry_before else 0.0

            # Count total entries to process
            total_entries = (
                self.db.query(Khatabook)
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.is_deleted.is_(False),
                    Khatabook.expense_date >= from_date
                )
                .count()
            )

            if total_entries == 0:
                db_logger.info(f"No entries to recalculate for user {user_id}")
                return 0

            db_logger.info(f"Processing {total_entries} entries in batches of {batch_size}")

            # Process entries in batches for memory efficiency
            total_updated = 0
            current_balance = starting_balance
            offset = 0

            while offset < total_entries:
                # Get batch of entries
                batch_entries = (
                    self.db.query(Khatabook)
                    .filter(
                        Khatabook.created_by == user_id,
                        Khatabook.is_deleted.is_(False),
                        Khatabook.expense_date >= from_date
                    )
                    .order_by(
                        asc(Khatabook.expense_date),
                        asc(Khatabook.created_at),
                        asc(Khatabook.id)
                    )
                    .offset(offset)
                    .limit(batch_size)
                    .all()
                )

                if not batch_entries:
                    break

                # Calculate balances for this batch
                balance_updates = []
                for entry in batch_entries:
                    if entry.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT:
                        current_balance += entry.amount
                    elif entry.entry_type == KHATABOOK_ENTRY_TYPE_DEBIT:
                        current_balance -= entry.amount

                    # Only add to update list if balance changed
                    if entry.balance_after_entry != current_balance:
                        balance_updates.append((entry.uuid, current_balance))

                # Perform batch update for this batch
                if balance_updates:
                    batch_updated = self.batch_update_balances(balance_updates, batch_size=50)
                    total_updated += batch_updated

                offset += batch_size
                db_logger.debug(f"Processed batch {offset//batch_size}, updated {len(balance_updates)} entries")

            db_logger.info(f"Optimized recalculation completed: {total_updated} entries updated")
            return total_updated

        except Exception as e:
            db_logger.error(f"Error in optimized balance recalculation for user {user_id}: {str(e)}")
            raise

    def get_user_entries_count(self, user_id: UUID) -> int:
        """
        Get the total count of non-deleted entries for a user.

        This is used to determine whether to use optimized batch processing
        for users with large numbers of entries.

        Args:
            user_id: User UUID

        Returns:
            Count of non-deleted entries
        """
        try:
            count = (
                self.db.query(Khatabook)
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.is_deleted.is_(False)
                )
                .count()
            )

            db_logger.debug(f"User {user_id} has {count} khatabook entries")
            return count

        except Exception as e:
            db_logger.error(f"Error counting entries for user {user_id}: {str(e)}")
            raise

    def should_use_optimized_recalculation(self, user_id: UUID, threshold: int = 500) -> bool:
        """
        Determine whether to use optimized batch processing based on entry count.

        Args:
            user_id: User UUID
            threshold: Entry count threshold for using optimization

        Returns:
            True if optimized processing should be used
        """
        try:
            entry_count = self.get_user_entries_count(user_id)
            use_optimized = entry_count >= threshold

            if use_optimized:
                db_logger.info(f"Using optimized recalculation for user {user_id} ({entry_count} entries)")
            else:
                db_logger.debug(f"Using standard recalculation for user {user_id} ({entry_count} entries)")

            return use_optimized

        except Exception as e:
            db_logger.error(f"Error determining optimization strategy for user {user_id}: {str(e)}")
            # Default to standard processing on error
            return False

    def detect_and_fix_corrupted_balances(self, user_id: UUID, force_recalculation: bool = False) -> int:
        """
        Detect and fix corrupted balance_after_entry values for a user.

        This method is designed to handle existing data when deploying the new
        balance recalculation logic to production. It will:
        1. Check if balances are correctly calculated
        2. Fix any inconsistencies found
        3. Optionally force a complete recalculation

        Args:
            user_id: User UUID to check and fix
            force_recalculation: If True, recalculate all balances regardless of current state

        Returns:
            Number of entries that were corrected
        """
        try:
            db_logger.info(f"Detecting and fixing corrupted balances for user {user_id}")

            # Get all entries in chronological order
            entries = self.get_user_entries_chronological(user_id)

            if not entries:
                db_logger.info(f"No entries found for user {user_id}")
                return 0

            # Calculate what the balances should be
            correct_balances = self.calculate_running_balance(entries, 0.0)

            # Check for inconsistencies
            corrupted_entries = []
            for entry, correct_balance in correct_balances:
                if force_recalculation or entry.balance_after_entry != correct_balance:
                    corrupted_entries.append((entry, correct_balance))

            if not corrupted_entries:
                db_logger.info(f"All balances are correct for user {user_id}")
                return 0

            db_logger.info(f"Found {len(corrupted_entries)} corrupted balances for user {user_id}")

            # Fix the corrupted balances
            updated_count = 0
            for entry, correct_balance in corrupted_entries:
                old_balance = entry.balance_after_entry
                entry.balance_after_entry = correct_balance
                updated_count += 1
                db_logger.debug(f"Fixed entry {entry.uuid}: {old_balance} -> {correct_balance}")

            # Flush changes
            self.db.flush()

            db_logger.info(f"Fixed {updated_count} corrupted balances for user {user_id}")
            return updated_count

        except Exception as e:
            db_logger.error(f"Error detecting/fixing corrupted balances for user {user_id}: {str(e)}")
            raise

    def ensure_user_balances_are_correct(self, user_id: UUID) -> bool:
        """
        Ensure that a user's balances are correct before performing operations.

        This method is called automatically when creating new entries to ensure
        that existing data is correct. It's designed to be safe for production
        and will only fix balances if they are actually incorrect.

        Args:
            user_id: User UUID to check

        Returns:
            True if balances were already correct or successfully fixed
        """
        try:
            # Quick check: if user has no entries, balances are correct
            entry_count = self.get_user_entries_count(user_id)
            if entry_count == 0:
                return True

            # For users with many entries, be more conservative
            if entry_count > 1000:
                db_logger.info(f"User {user_id} has {entry_count} entries, skipping automatic balance check")
                return True

            # Check and fix balances for smaller ledgers
            fixed_count = self.detect_and_fix_corrupted_balances(user_id)

            if fixed_count > 0:
                db_logger.info(f"Automatically fixed {fixed_count} balance inconsistencies for user {user_id}")

            return True

        except Exception as e:
            db_logger.error(f"Error ensuring balances are correct for user {user_id}: {str(e)}")
            # Don't fail the main operation if balance checking fails
            return False
