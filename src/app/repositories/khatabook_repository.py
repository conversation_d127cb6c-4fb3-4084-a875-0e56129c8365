"""
Khatabook repository with comprehensive balance recalculation logic.
"""

from datetime import datetime
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, asc, desc, func
from sqlalchemy.orm import Session, joinedload

from src.app.database.models import (
    Khatabook,
    KhatabookBalance,
    KhatabookFile,
    KhatabookItem,
    Payment,
)
from src.app.repositories.base_repository import BaseRepository
from src.app.schemas.constants import K<PERSON><PERSON>BOOK_ENTRY_TYPE_CREDIT, KHATABOOK_ENTRY_TYPE_DEBIT
from src.app.utils.logging_config import get_database_logger

# Initialize logger
db_logger = get_database_logger()


class KhatabookRepository(BaseRepository[Khatabook]):
    """
    Repository for Khatabook operations with comprehensive balance recalculation logic.
    
    This repository handles:
    1. Creating new credit/debit khatabook entries with correct balance_after_entry
    2. Recalculating balances when entries are deleted from any position
    3. Handling historical and future entry insertions
    4. Maintaining chronological ordering based on expense_date
    """
    
    def __init__(self, db: Session):
        super().__init__(K<PERSON>ab<PERSON>, db)
    
    def get_user_entries_chronological(
        self, 
        user_id: UUID, 
        include_deleted: bool = False,
        from_date: Optional[datetime] = None
    ) -> List[Khatabook]:
        """
        Get user's khatabook entries in chronological order by expense_date.
        
        Args:
            user_id: User UUID
            include_deleted: Whether to include soft-deleted entries
            from_date: Optional date to filter entries from (inclusive)
            
        Returns:
            List of khatabook entries ordered by expense_date, then by created_at
        """
        try:
            query = (
                self.db.query(Khatabook)
                .filter(Khatabook.created_by == user_id)
            )
            
            if not include_deleted:
                query = query.filter(Khatabook.is_deleted.is_(False))
            
            if from_date:
                query = query.filter(Khatabook.expense_date >= from_date)
            
            # Order by expense_date first, then by created_at for entries on same date
            # Use id as final tiebreaker for consistent ordering
            entries = query.order_by(
                asc(Khatabook.expense_date),
                asc(Khatabook.created_at),
                asc(Khatabook.id)
            ).all()
            
            db_logger.debug(f"Retrieved {len(entries)} chronological entries for user {user_id}")
            return entries
            
        except Exception as e:
            db_logger.error(f"Error getting chronological entries for user {user_id}: {str(e)}")
            raise
    
    def get_last_entry_before_date(self, user_id: UUID, before_date: datetime) -> Optional[Khatabook]:
        """
        Get the last khatabook entry before a specific date for balance calculation.
        
        Args:
            user_id: User UUID
            before_date: Date to search before
            
        Returns:
            Last entry before the given date, or None if no entries exist
        """
        try:
            entry = (
                self.db.query(Khatabook)
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.is_deleted.is_(False),
                    Khatabook.expense_date < before_date
                )
                .order_by(
                    desc(Khatabook.expense_date),
                    desc(Khatabook.created_at),
                    desc(Khatabook.id)
                )
                .first()
            )
            
            db_logger.debug(f"Found last entry before {before_date} for user {user_id}: {entry.uuid if entry else None}")
            return entry
            
        except Exception as e:
            db_logger.error(f"Error getting last entry before date for user {user_id}: {str(e)}")
            raise
    
    def calculate_running_balance(self, entries: List[Khatabook], starting_balance: float = 0.0) -> List[Tuple[Khatabook, float]]:
        """
        Calculate running balance for a list of chronologically ordered entries.
        
        Args:
            entries: List of khatabook entries in chronological order
            starting_balance: Starting balance before the first entry
            
        Returns:
            List of tuples (entry, balance_after_entry)
        """
        try:
            result = []
            current_balance = starting_balance
            
            for entry in entries:
                if entry.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT:
                    current_balance += entry.amount
                elif entry.entry_type == KHATABOOK_ENTRY_TYPE_DEBIT:
                    current_balance -= entry.amount
                else:
                    db_logger.warning(f"Unknown entry type '{entry.entry_type}' for entry {entry.uuid}")
                    continue
                
                result.append((entry, current_balance))
                db_logger.debug(f"Entry {entry.uuid}: {entry.entry_type} {entry.amount} -> Balance: {current_balance}")
            
            return result
            
        except Exception as e:
            db_logger.error(f"Error calculating running balance: {str(e)}")
            raise
    
    def get_user_available_balance(self, user_id: UUID) -> float:
        """
        Calculate user's available balance from transferred self-payments minus total spent.
        This is used for debit entry balance calculations.
        
        Args:
            user_id: User UUID
            
        Returns:
            Available balance amount
        """
        try:
            # Get total from transferred self-payments
            transferred_self_payments = (
                self.db.query(Payment)
                .filter(
                    Payment.created_by == user_id,
                    Payment.self_payment == True,
                    Payment.status == "transferred",
                    Payment.is_deleted == False,
                )
                .all()
            )
            
            user_available_balance = sum(payment.amount for payment in transferred_self_payments)
            
            # Get total spent (debit entries)
            total_spent = (
                self.db.query(func.sum(Khatabook.amount))
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_DEBIT,
                    Khatabook.is_deleted == False
                )
                .scalar() or 0.0
            )
            
            available_balance = user_available_balance - total_spent
            
            db_logger.debug(f"User {user_id} available balance: {user_available_balance} - {total_spent} = {available_balance}")
            return available_balance
            
        except Exception as e:
            db_logger.error(f"Error calculating available balance for user {user_id}: {str(e)}")
            raise

    def recalculate_balances_from_date(self, user_id: UUID, from_date: datetime) -> int:
        """
        Recalculate balance_after_entry for all entries from a specific date onwards.

        This method is called when:
        1. A new entry is inserted with a past expense_date
        2. An entry is deleted from the middle of the chronological sequence
        3. An entry's expense_date is modified

        Args:
            user_id: User UUID
            from_date: Date from which to recalculate balances (inclusive)

        Returns:
            Number of entries updated
        """
        try:
            db_logger.info(f"Starting balance recalculation for user {user_id} from date {from_date}")

            # Get the last entry before the from_date to establish starting balance
            last_entry_before = self.get_last_entry_before_date(user_id, from_date)
            starting_balance = last_entry_before.balance_after_entry if last_entry_before else 0.0

            # Get all entries from the specified date onwards
            entries_to_update = self.get_user_entries_chronological(user_id, from_date=from_date)

            if not entries_to_update:
                db_logger.info(f"No entries found to recalculate for user {user_id} from {from_date}")
                return 0

            # Calculate new balances
            balance_calculations = self.calculate_running_balance(entries_to_update, starting_balance)

            # Update entries with new balance_after_entry values
            updated_count = 0
            for entry, new_balance in balance_calculations:
                if entry.balance_after_entry != new_balance:
                    entry.balance_after_entry = new_balance
                    updated_count += 1
                    db_logger.debug(f"Updated entry {entry.uuid} balance to {new_balance}")

            # Flush changes to database
            self.db.flush()

            db_logger.info(f"Recalculated balances for {updated_count} entries for user {user_id}")
            return updated_count

        except Exception as e:
            db_logger.error(f"Error recalculating balances from date for user {user_id}: {str(e)}")
            raise

    def create_entry_with_balance_calculation(self, **entry_data) -> Khatabook:
        """
        Create a new khatabook entry with proper balance_after_entry calculation.

        This method handles both credit and debit entries and determines whether
        balance recalculation is needed for existing entries.

        Args:
            **entry_data: Khatabook entry data

        Returns:
            Created khatabook entry with correct balance_after_entry
        """
        try:
            user_id = entry_data.get('created_by')
            expense_date = entry_data.get('expense_date', datetime.now())
            entry_type = entry_data.get('entry_type', KHATABOOK_ENTRY_TYPE_DEBIT)
            amount = entry_data.get('amount', 0.0)

            db_logger.info(f"Creating {entry_type} entry for user {user_id} with amount {amount} on {expense_date}")

            # Check if this is a historical entry (expense_date is before existing entries)
            latest_entry = (
                self.db.query(Khatabook)
                .filter(
                    Khatabook.created_by == user_id,
                    Khatabook.is_deleted.is_(False)
                )
                .order_by(desc(Khatabook.expense_date), desc(Khatabook.created_at))
                .first()
            )

            is_historical_entry = latest_entry and expense_date < latest_entry.expense_date

            # Calculate balance_after_entry for the new entry
            if entry_type == KHATABOOK_ENTRY_TYPE_DEBIT:
                # For debit entries, use available balance calculation
                current_available_balance = self.get_user_available_balance(user_id)
                balance_after_entry = current_available_balance - amount
            else:
                # For credit entries, use running balance calculation
                last_entry_before = self.get_last_entry_before_date(user_id, expense_date)
                starting_balance = last_entry_before.balance_after_entry if last_entry_before else 0.0
                balance_after_entry = starting_balance + amount

            # Create the entry
            entry_data['balance_after_entry'] = balance_after_entry
            new_entry = self.create(**entry_data)

            db_logger.info(f"Created entry {new_entry.uuid} with balance_after_entry: {balance_after_entry}")

            # If this is a historical entry, recalculate balances for subsequent entries
            if is_historical_entry:
                db_logger.info(f"Historical entry detected, recalculating subsequent balances")
                self.recalculate_balances_from_date(user_id, expense_date)

            return new_entry

        except Exception as e:
            db_logger.error(f"Error creating entry with balance calculation: {str(e)}")
            raise
