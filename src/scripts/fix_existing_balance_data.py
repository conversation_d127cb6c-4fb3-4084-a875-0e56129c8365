#!/usr/bin/env python3
"""
Script to fix existing balance_after_entry data for production deployment.

This script is designed to be run ONCE when deploying the new balance recalculation
logic to production. It will:

1. Identify users with incorrect balance_after_entry values
2. Recalculate balances using the correct chronological order (expense_date)
3. Update the database with correct values
4. Provide detailed logging and progress tracking
5. Handle large ledgers efficiently

Usage:
    python src/scripts/fix_existing_balance_data.py [--dry-run] [--user-id UUID] [--batch-size N]
"""

import sys
import argparse
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import select, asc, func
from uuid import UUID
from datetime import datetime

# Add project root
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Imports
from src.app.database.database import SessionLocal, settings
from src.app.database.models import Khatabook
from src.app.repositories.khatabook_repository import KhatabookRepository
from src.app.utils.logging_config import setup_logging, get_logger

# Setup logging
setup_logging(log_level="INFO", log_dir=settings.LOG_DIR, app_name="fix_balance_data")
logger = get_logger("fix_balance_data")


def analyze_user_balance_integrity(db: Session, user_id: UUID) -> dict:
    """
    Analyze the integrity of balance_after_entry values for a specific user.
    
    Returns:
        dict: Analysis results including corruption status and statistics
    """
    try:
        repo = KhatabookRepository(db)
        
        # Get all entries in chronological order
        entries = repo.get_user_entries_chronological(user_id)
        
        if not entries:
            return {
                'user_id': user_id,
                'total_entries': 0,
                'corrupted_entries': 0,
                'is_corrupted': False,
                'needs_fix': False
            }
        
        # Calculate what balances should be
        correct_balances = repo.calculate_running_balance(entries, 0.0)
        
        # Check for inconsistencies
        corrupted_count = 0
        null_balance_count = 0
        
        for entry, correct_balance in correct_balances:
            if entry.balance_after_entry is None:
                null_balance_count += 1
                corrupted_count += 1
            elif abs(entry.balance_after_entry - correct_balance) > 0.01:  # Allow for small floating point differences
                corrupted_count += 1
        
        is_corrupted = corrupted_count > 0
        
        return {
            'user_id': user_id,
            'total_entries': len(entries),
            'corrupted_entries': corrupted_count,
            'null_balance_entries': null_balance_count,
            'is_corrupted': is_corrupted,
            'needs_fix': is_corrupted,
            'first_entry_date': entries[0].expense_date if entries else None,
            'last_entry_date': entries[-1].expense_date if entries else None
        }
        
    except Exception as e:
        logger.error(f"Error analyzing user {user_id}: {str(e)}")
        return {
            'user_id': user_id,
            'error': str(e),
            'needs_fix': False
        }


def fix_user_balances(db: Session, user_id: UUID, dry_run: bool = False) -> dict:
    """
    Fix balance_after_entry values for a specific user.
    
    Returns:
        dict: Results of the fix operation
    """
    try:
        repo = KhatabookRepository(db)
        
        if dry_run:
            # Just analyze, don't fix
            analysis = analyze_user_balance_integrity(db, user_id)
            return {
                'user_id': user_id,
                'dry_run': True,
                'would_fix': analysis['corrupted_entries'],
                'analysis': analysis
            }
        
        # Actually fix the balances
        fixed_count = repo.detect_and_fix_corrupted_balances(user_id, force_recalculation=True)
        
        return {
            'user_id': user_id,
            'dry_run': False,
            'fixed_entries': fixed_count,
            'success': True
        }
        
    except Exception as e:
        logger.error(f"Error fixing balances for user {user_id}: {str(e)}")
        return {
            'user_id': user_id,
            'error': str(e),
            'success': False
        }


def main():
    parser = argparse.ArgumentParser(description='Fix existing balance_after_entry data')
    parser.add_argument('--dry-run', action='store_true', help='Analyze only, do not make changes')
    parser.add_argument('--user-id', type=str, help='Fix balances for specific user only')
    parser.add_argument('--batch-size', type=int, default=50, help='Number of users to process in each batch')
    parser.add_argument('--force', action='store_true', help='Force recalculation even if balances appear correct')
    
    args = parser.parse_args()
    
    db: Session = SessionLocal()
    
    try:
        logger.info("🚀 Starting balance data fix operation")
        logger.info(f"Mode: {'DRY RUN' if args.dry_run else 'LIVE FIX'}")
        
        if args.user_id:
            # Fix specific user
            user_uuid = UUID(args.user_id)
            logger.info(f"Processing specific user: {user_uuid}")
            
            result = fix_user_balances(db, user_uuid, dry_run=args.dry_run)
            
            if result.get('success', False):
                logger.info(f"✅ Fixed {result['fixed_entries']} entries for user {user_uuid}")
            elif args.dry_run:
                logger.info(f"📊 Would fix {result['would_fix']} entries for user {user_uuid}")
            else:
                logger.error(f"❌ Failed to fix user {user_uuid}: {result.get('error', 'Unknown error')}")
        
        else:
            # Process all users
            logger.info("🔍 Finding all users with khatabook entries...")
            
            users = db.query(Khatabook.created_by).filter(
                Khatabook.is_deleted == False
            ).distinct().all()
            
            total_users = len(users)
            logger.info(f"👥 Found {total_users} users with khatabook entries")
            
            # Statistics tracking
            stats = {
                'total_users': total_users,
                'processed_users': 0,
                'corrupted_users': 0,
                'fixed_users': 0,
                'total_entries_fixed': 0,
                'errors': 0
            }
            
            # Process users in batches
            for i in range(0, total_users, args.batch_size):
                batch_users = users[i:i + args.batch_size]
                batch_num = i // args.batch_size + 1
                total_batches = (total_users + args.batch_size - 1) // args.batch_size
                
                logger.info(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch_users)} users)")
                
                for user_tuple in batch_users:
                    user_id = user_tuple[0]
                    
                    try:
                        # First analyze
                        analysis = analyze_user_balance_integrity(db, user_id)
                        stats['processed_users'] += 1
                        
                        if analysis.get('is_corrupted', False):
                            stats['corrupted_users'] += 1
                            
                            if not args.dry_run:
                                # Fix the user's balances
                                fix_result = fix_user_balances(db, user_id, dry_run=False)
                                
                                if fix_result.get('success', False):
                                    stats['fixed_users'] += 1
                                    stats['total_entries_fixed'] += fix_result.get('fixed_entries', 0)
                                    logger.info(f"✅ Fixed {fix_result['fixed_entries']} entries for user {user_id}")
                                else:
                                    stats['errors'] += 1
                                    logger.error(f"❌ Failed to fix user {user_id}")
                            else:
                                logger.info(f"📊 User {user_id}: would fix {analysis['corrupted_entries']} entries")
                        
                        # Progress update every 10 users
                        if stats['processed_users'] % 10 == 0:
                            logger.info(f"Progress: {stats['processed_users']}/{total_users} users processed")
                    
                    except Exception as e:
                        stats['errors'] += 1
                        logger.error(f"Error processing user {user_id}: {str(e)}")
                
                # Commit batch if not dry run
                if not args.dry_run:
                    db.commit()
                    logger.info(f"💾 Committed batch {batch_num}")
            
            # Final statistics
            logger.info("📊 Final Statistics:")
            logger.info(f"   Total users: {stats['total_users']}")
            logger.info(f"   Processed users: {stats['processed_users']}")
            logger.info(f"   Corrupted users: {stats['corrupted_users']}")
            
            if not args.dry_run:
                logger.info(f"   Fixed users: {stats['fixed_users']}")
                logger.info(f"   Total entries fixed: {stats['total_entries_fixed']}")
            else:
                logger.info(f"   Users that would be fixed: {stats['corrupted_users']}")
            
            logger.info(f"   Errors: {stats['errors']}")
        
        if not args.dry_run:
            db.commit()
            logger.info("🎉 Balance fix operation completed successfully!")
        else:
            logger.info("🔍 Dry run completed - no changes made")
    
    except SQLAlchemyError as e:
        db.rollback()
        logger.critical("❌ Database error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    except Exception as e:
        db.rollback()
        logger.critical("❌ Unexpected error occurred. Rolling back.")
        logger.exception(e)
        sys.exit(1)
    finally:
        db.close()
        logger.info("🔒 Database session closed.")


if __name__ == "__main__":
    main()
